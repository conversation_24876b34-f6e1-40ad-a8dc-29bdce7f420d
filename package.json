{"name": "ai-voice-assistant", "version": "1.0.0", "description": "Enterprise-grade AI Voice Assistant powered by Google's Gemini technology", "type": "module", "main": "src/main.ts", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext .ts,.js", "lint:fix": "eslint src --ext .ts,.js --fix", "format": "prettier --write src/**/*.{ts,js,css,html}", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "clean": "rm -rf dist node_modules/.vite", "analyze": "vite-bundle-analyzer dist"}, "dependencies": {"@google/genai": "^0.2.1", "lit": "^3.1.0"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "@vitejs/plugin-legacy": "^5.2.0", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-lit": "^1.10.1", "eslint-plugin-wc": "^2.0.4", "postcss": "^8.4.32", "prettier": "^3.1.0", "typescript": "^5.3.0", "vite": "^5.0.0", "vite-bundle-analyzer": "^0.7.0", "vite-plugin-pwa": "^0.17.4", "vitest": "^1.0.0", "workbox-window": "^7.0.0"}, "keywords": ["ai", "voice-assistant", "gemini", "conversational-ai", "enterprise", "speech-recognition", "web-components", "lit", "typescript"], "author": "Enterprise AI Solutions", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/enterprise-ai/voice-assistant.git"}, "bugs": {"url": "https://github.com/enterprise-ai/voice-assistant/issues"}, "homepage": "https://ai-voice-assistant.com", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}