{"name": "AI Voice Assistant - Enterprise Conversational AI", "short_name": "AI Voice Assistant", "description": "Enterprise-grade AI Voice Assistant powered by Google's Gemini technology for seamless voice interactions.", "start_url": "/", "display": "standalone", "background_color": "#0a0a0a", "theme_color": "#2563eb", "orientation": "portrait-primary", "scope": "/", "lang": "en", "dir": "ltr", "categories": ["productivity", "business", "utilities"], "icons": [{"src": "/assets/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "screenshots": [{"src": "/assets/screenshot-desktop.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "AI Voice Assistant <PERSON><PERSON><PERSON> Interface"}, {"src": "/assets/screenshot-mobile.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "AI Voice Assistant Mobile Interface"}], "shortcuts": [{"name": "Start Voice Session", "short_name": "Voice", "description": "Start a new voice conversation", "url": "/?action=voice", "icons": [{"src": "/assets/shortcut-voice.png", "sizes": "96x96"}]}, {"name": "Settings", "short_name": "Settings", "description": "Configure voice assistant settings", "url": "/?action=settings", "icons": [{"src": "/assets/shortcut-settings.png", "sizes": "96x96"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "focus-existing"}}