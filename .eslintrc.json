{"root": true, "env": {"browser": true, "es2022": true, "node": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint", "lit", "wc"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-non-null-assertion": "warn", "@typescript-eslint/prefer-const": "error", "@typescript-eslint/no-var-requires": "error", "prefer-const": "error", "no-var": "error", "no-console": ["warn", {"allow": ["warn", "error"]}], "eqeqeq": ["error", "always"], "curly": ["error", "all"], "lit/no-invalid-html": "error", "lit/no-useless-template-literals": "error", "lit/attribute-value-entities": "error", "lit/binding-positions": "error", "lit/no-duplicate-template-bindings": "error", "lit/no-legacy-template-syntax": "error", "lit/no-template-bind": "error", "lit/no-template-map": "error", "wc/guard-super-call": "error", "wc/no-closed-shadow-root": "error", "wc/no-constructor-attributes": "error", "wc/no-invalid-element-name": "error", "wc/no-self-class": "error"}, "ignorePatterns": ["dist", "node_modules", "*.config.js", "*.config.ts"]}