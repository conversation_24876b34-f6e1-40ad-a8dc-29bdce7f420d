/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */

import {LitElement, css, html} from 'lit';
import {customElement, property} from 'lit/decorators.js';

@customElement('app-layout')
export class AppLayout extends LitElement {
  @property({type: String}) title = 'AI Voice Assistant';
  @property({type: String}) subtitle = 'Enterprise-grade conversational AI';
  @property({type: <PERSON>olean}) showHeader = true;
  @property({type: Boolean}) showFooter = true;

  static styles = css`
    :host {
      display: block;
      min-height: 100vh;
      background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0f0f0f 100%);
      position: relative;
      overflow: hidden;
    }

    /* Animated background elements */
    .background-elements {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;
      z-index: 1;
    }

    .background-elements::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle at 30% 20%, rgba(37, 99, 235, 0.1) 0%, transparent 50%),
                  radial-gradient(circle at 70% 80%, rgba(16, 185, 129, 0.08) 0%, transparent 50%),
                  radial-gradient(circle at 90% 10%, rgba(245, 158, 11, 0.06) 0%, transparent 50%);
      animation: backgroundFloat 20s ease-in-out infinite;
    }

    .background-elements::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: 
        linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.02) 50%, transparent 100%),
        linear-gradient(0deg, transparent 0%, rgba(255, 255, 255, 0.01) 50%, transparent 100%);
      background-size: 100px 100px, 100px 100px;
      animation: gridMove 30s linear infinite;
    }

    .layout-container {
      position: relative;
      z-index: 2;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }

    .header {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      z-index: 10;
      padding: var(--spacing-6) var(--spacing-8);
      background: rgba(255, 255, 255, 0.02);
      backdrop-filter: blur(20px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .brand {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
    }

    .brand-icon {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
      border-radius: var(--radius-xl);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: var(--shadow-lg);
    }

    .brand-icon svg {
      width: 24px;
      height: 24px;
      fill: white;
    }

    .brand-text {
      display: flex;
      flex-direction: column;
    }

    .brand-title {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-text-primary);
      line-height: var(--line-height-tight);
      margin: 0;
    }

    .brand-subtitle {
      font-size: var(--font-size-sm);
      color: var(--color-text-tertiary);
      font-weight: var(--font-weight-medium);
      margin: 0;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: var(--spacing-4);
    }

    .status-indicator {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      padding: var(--spacing-2) var(--spacing-3);
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
    }

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: var(--color-success-500);
      animation: pulse 2s infinite;
    }

    .main-content {
      flex: 1;
      position: relative;
      padding-top: ${this.showHeader ? '120px' : '0'};
      padding-bottom: ${this.showFooter ? '80px' : '0'};
    }

    .footer {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 10;
      padding: var(--spacing-4) var(--spacing-8);
      background: rgba(255, 255, 255, 0.02);
      backdrop-filter: blur(20px);
      border-top: 1px solid rgba(255, 255, 255, 0.05);
    }

    .footer-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: var(--font-size-sm);
      color: var(--color-text-tertiary);
    }

    .footer-links {
      display: flex;
      gap: var(--spacing-6);
    }

    .footer-link {
      color: var(--color-text-tertiary);
      text-decoration: none;
      transition: color var(--transition-fast);
    }

    .footer-link:hover {
      color: var(--color-text-secondary);
    }

    .copyright {
      font-weight: var(--font-weight-medium);
    }

    @keyframes backgroundFloat {
      0%, 100% {
        transform: translate(0, 0) rotate(0deg);
      }
      33% {
        transform: translate(30px, -30px) rotate(120deg);
      }
      66% {
        transform: translate(-20px, 20px) rotate(240deg);
      }
    }

    @keyframes gridMove {
      0% {
        transform: translate(0, 0);
      }
      100% {
        transform: translate(100px, 100px);
      }
    }

    @keyframes pulse {
      0%, 100% {
        opacity: 1;
      }
      50% {
        opacity: 0.5;
      }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .header {
        padding: var(--spacing-4) var(--spacing-4);
      }

      .brand-title {
        font-size: var(--font-size-lg);
      }

      .brand-subtitle {
        font-size: var(--font-size-xs);
      }

      .header-actions {
        gap: var(--spacing-2);
      }

      .status-indicator {
        padding: var(--spacing-1-5) var(--spacing-2);
        font-size: var(--font-size-xs);
      }

      .footer {
        padding: var(--spacing-3) var(--spacing-4);
      }

      .footer-content {
        flex-direction: column;
        gap: var(--spacing-2);
        text-align: center;
      }

      .footer-links {
        gap: var(--spacing-4);
      }

      .main-content {
        padding-top: ${this.showHeader ? '100px' : '0'};
        padding-bottom: ${this.showFooter ? '100px' : '0'};
      }
    }

    @media (max-width: 480px) {
      .brand-text {
        display: none;
      }

      .header-actions {
        flex-direction: column;
        align-items: flex-end;
        gap: var(--spacing-1);
      }

      .footer-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: var(--spacing-2);
      }
    }
  `;

  render() {
    return html`
      <div class="background-elements"></div>
      
      <div class="layout-container">
        ${this.showHeader ? html`
          <header class="header">
            <div class="header-content">
              <div class="brand">
                <div class="brand-icon">
                  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 21H5V3H13V9H19Z"/>
                  </svg>
                </div>
                <div class="brand-text">
                  <h1 class="brand-title">${this.title}</h1>
                  <p class="brand-subtitle">${this.subtitle}</p>
                </div>
              </div>
              
              <div class="header-actions">
                <div class="status-indicator">
                  <div class="status-dot"></div>
                  <span>Online</span>
                </div>
              </div>
            </div>
          </header>
        ` : ''}

        <main class="main-content">
          <slot></slot>
        </main>

        ${this.showFooter ? html`
          <footer class="footer">
            <div class="footer-content">
              <div class="footer-links">
                <a href="#" class="footer-link">Privacy</a>
                <a href="#" class="footer-link">Terms</a>
                <a href="#" class="footer-link">Support</a>
                <a href="#" class="footer-link">Documentation</a>
              </div>
              <div class="copyright">
                © 2024 Enterprise AI Solutions. All rights reserved.
              </div>
            </div>
          </footer>
        ` : ''}
      </div>
    `;
  }
}
