/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * Creates a blob from PCM audio data for transmission
 */
export function createBlob(pcmData: Float32Array): Blob {
  // Convert Float32Array to Int16Array for better compression
  const int16Array = new Int16Array(pcmData.length);
  for (let i = 0; i < pcmData.length; i++) {
    // Clamp values to prevent overflow
    const sample = Math.max(-1, Math.min(1, pcmData[i]));
    int16Array[i] = sample * 0x7fff;
  }
  
  return new Blob([int16Array.buffer], { type: 'audio/pcm' });
}

/**
 * Decodes base64 audio data
 */
export function decode(base64Data: string): ArrayBuffer {
  try {
    const binaryString = atob(base64Data);
    const bytes = new Uint8Array(binaryString.length);
    
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    
    return bytes.buffer;
  } catch (error) {
    console.error('Failed to decode base64 audio data:', error);
    throw new Error('Invalid base64 audio data');
  }
}

/**
 * Decodes audio data into an AudioBuffer
 */
export async function decodeAudioData(
  arrayBuffer: ArrayBuffer,
  audioContext: AudioContext,
  sampleRate: number,
  numberOfChannels: number
): Promise<AudioBuffer> {
  try {
    // Try to decode using the built-in decoder first
    try {
      return await audioContext.decodeAudioData(arrayBuffer);
    } catch (decodeError) {
      console.warn('Built-in decoder failed, using manual decoding:', decodeError);
    }
    
    // Manual decoding for PCM data
    const dataView = new DataView(arrayBuffer);
    const numberOfSamples = arrayBuffer.byteLength / 2; // 16-bit samples
    
    const audioBuffer = audioContext.createBuffer(
      numberOfChannels,
      numberOfSamples,
      sampleRate
    );
    
    const channelData = audioBuffer.getChannelData(0);
    
    for (let i = 0; i < numberOfSamples; i++) {
      // Read 16-bit signed integer and convert to float
      const sample = dataView.getInt16(i * 2, true); // little-endian
      channelData[i] = sample / 0x7fff; // Normalize to [-1, 1]
    }
    
    return audioBuffer;
  } catch (error) {
    console.error('Failed to decode audio data:', error);
    throw new Error('Failed to decode audio data');
  }
}

/**
 * Formats time duration in a human-readable format
 */
export function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * Debounce function to limit the rate of function calls
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function to limit the rate of function calls
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * Checks if the browser supports the required Web APIs
 */
export function checkBrowserSupport(): {
  supported: boolean;
  missing: string[];
} {
  const missing: string[] = [];
  
  if (!navigator.mediaDevices?.getUserMedia) {
    missing.push('MediaDevices.getUserMedia');
  }
  
  if (!window.AudioContext && !window.webkitAudioContext) {
    missing.push('AudioContext');
  }
  
  if (!window.WebSocket) {
    missing.push('WebSocket');
  }
  
  if (!window.fetch) {
    missing.push('Fetch API');
  }
  
  return {
    supported: missing.length === 0,
    missing
  };
}

/**
 * Gets the optimal audio constraints for the current device
 */
export function getOptimalAudioConstraints(): MediaStreamConstraints {
  return {
    audio: {
      echoCancellation: true,
      noiseSuppression: true,
      autoGainControl: true,
      sampleRate: 16000,
      channelCount: 1,
      sampleSize: 16
    },
    video: false
  };
}

/**
 * Analyzes audio levels for visualization
 */
export function analyzeAudioLevel(
  analyser: AnalyserNode,
  dataArray: Uint8Array
): number {
  analyser.getByteFrequencyData(dataArray);
  
  let sum = 0;
  for (let i = 0; i < dataArray.length; i++) {
    sum += dataArray[i];
  }
  
  return sum / dataArray.length / 255; // Normalize to [0, 1]
}

/**
 * Creates a visual representation of audio frequency data
 */
export function getFrequencyBands(
  analyser: AnalyserNode,
  dataArray: Uint8Array,
  bandCount: number = 8
): number[] {
  analyser.getByteFrequencyData(dataArray);
  
  const bands: number[] = [];
  const bandSize = Math.floor(dataArray.length / bandCount);
  
  for (let i = 0; i < bandCount; i++) {
    let sum = 0;
    const start = i * bandSize;
    const end = start + bandSize;
    
    for (let j = start; j < end && j < dataArray.length; j++) {
      sum += dataArray[j];
    }
    
    bands.push(sum / bandSize / 255); // Normalize to [0, 1]
  }
  
  return bands;
}

/**
 * Error handling utility for async operations
 */
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  errorMessage: string
): Promise<T | null> {
  try {
    return await operation();
  } catch (error) {
    console.error(errorMessage, error);
    return null;
  }
}

/**
 * Local storage utility with error handling
 */
export const storage = {
  get<T>(key: string, defaultValue: T): T {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error('Failed to get item from localStorage:', error);
      return defaultValue;
    }
  },
  
  set<T>(key: string, value: T): boolean {
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error('Failed to set item in localStorage:', error);
      return false;
    }
  },
  
  remove(key: string): boolean {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('Failed to remove item from localStorage:', error);
      return false;
    }
  }
};
