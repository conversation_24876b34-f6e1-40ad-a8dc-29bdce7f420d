/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */

import './styles/design-system.css';
import './components/app-layout';
import './components/gdm-live-audio';

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
  // Create the main application structure
  const app = document.createElement('app-layout');
  app.setAttribute('title', 'AI Voice Assistant');
  app.setAttribute('subtitle', 'Enterprise-grade conversational AI powered by Gemini');
  
  // Create the audio component
  const audioComponent = document.createElement('gdm-live-audio');
  app.appendChild(audioComponent);
  
  // Replace the body content
  document.body.innerHTML = '';
  document.body.appendChild(app);
  
  // Add global error handling
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
  });
  
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
  });
  
  // Add performance monitoring
  if ('performance' in window) {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        console.log('Page load performance:', {
          domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
          loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
          totalTime: perfData.loadEventEnd - perfData.fetchStart
        });
      }, 0);
    });
  }
});
