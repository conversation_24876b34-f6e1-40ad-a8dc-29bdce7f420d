<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Enterprise-grade AI Voice Assistant powered by Google's Gemini technology. Experience seamless voice interactions with advanced conversational AI.">
  <meta name="keywords" content="AI, voice assistant, Gemini, conversational AI, enterprise, speech recognition">
  <meta name="author" content="Enterprise AI Solutions">
  <meta name="robots" content="index, follow">
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://ai-voice-assistant.com/">
  <meta property="og:title" content="AI Voice Assistant - Enterprise Conversational AI">
  <meta property="og:description" content="Experience the future of voice interaction with our enterprise-grade AI assistant powered by Google's Gemini technology.">
  <meta property="og:image" content="/assets/og-image.jpg">
  
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://ai-voice-assistant.com/">
  <meta property="twitter:title" content="AI Voice Assistant - Enterprise Conversational AI">
  <meta property="twitter:description" content="Experience the future of voice interaction with our enterprise-grade AI assistant powered by Google's Gemini technology.">
  <meta property="twitter:image" content="/assets/twitter-image.jpg">
  
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/assets/favicon.svg">
  <link rel="icon" type="image/png" href="/assets/favicon.png">
  <link rel="apple-touch-icon" href="/assets/apple-touch-icon.png">
  
  <!-- Theme Color -->
  <meta name="theme-color" content="#2563eb">
  <meta name="msapplication-TileColor" content="#2563eb">
  
  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  
  <!-- Security Headers -->
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="X-Frame-Options" content="DENY">
  <meta http-equiv="X-XSS-Protection" content="1; mode=block">
  <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">
  
  <!-- PWA Manifest -->
  <link rel="manifest" href="/manifest.json">
  
  <title>AI Voice Assistant - Enterprise Conversational AI</title>
  
  <style>
    /* Critical CSS for initial load */
    html {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.5;
      -webkit-text-size-adjust: 100%;
      scroll-behavior: smooth;
    }
    
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0f0f0f 100%);
      color: #ffffff;
      min-height: 100vh;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      text-rendering: optimizeLegibility;
    }
    
    /* Loading spinner */
    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0f0f0f 100%);
      z-index: 9999;
      transition: opacity 0.3s ease-out;
    }
    
    .loading-spinner {
      width: 48px;
      height: 48px;
      border: 3px solid rgba(255, 255, 255, 0.1);
      border-top: 3px solid #2563eb;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    .loading-text {
      margin-top: 16px;
      color: rgba(255, 255, 255, 0.8);
      font-size: 14px;
      font-weight: 500;
      text-align: center;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* Hide loading when app is ready */
    .app-ready .loading-container {
      opacity: 0;
      pointer-events: none;
    }
    
    /* Focus management */
    :focus {
      outline: 2px solid #2563eb;
      outline-offset: 2px;
    }
    
    :focus:not(:focus-visible) {
      outline: none;
    }
    
    /* Selection styles */
    ::selection {
      background-color: #2563eb;
      color: white;
    }
    
    /* Scrollbar styles */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    
    ::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
    }
    
    ::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 4px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.15);
    }
    
    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
      *,
      *::before,
      *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
      }
    }
  </style>
</head>
<body>
  <!-- Loading Screen -->
  <div class="loading-container" id="loading">
    <div>
      <div class="loading-spinner"></div>
      <div class="loading-text">Initializing AI Assistant...</div>
    </div>
  </div>
  
  <!-- Fallback content for users with JavaScript disabled -->
  <noscript>
    <div style="
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: #0a0a0a;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    ">
      <div>
        <h1 style="margin-bottom: 16px; color: #2563eb;">JavaScript Required</h1>
        <p style="margin-bottom: 16px; color: rgba(255, 255, 255, 0.8);">
          This AI Voice Assistant requires JavaScript to function properly.
        </p>
        <p style="color: rgba(255, 255, 255, 0.6);">
          Please enable JavaScript in your browser settings and refresh the page.
        </p>
      </div>
    </div>
  </noscript>
  
  <!-- Main application will be inserted here -->
  
  <script type="module" src="/src/main.ts"></script>
  
  <script>
    // Remove loading screen when app is ready
    window.addEventListener('load', () => {
      setTimeout(() => {
        document.body.classList.add('app-ready');
        setTimeout(() => {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.remove();
          }
        }, 300);
      }, 500);
    });
    
    // Service Worker registration for PWA
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then((registration) => {
            console.log('SW registered: ', registration);
          })
          .catch((registrationError) => {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }
    
    // Basic error tracking
    window.addEventListener('error', (event) => {
      console.error('Application error:', event.error);
    });
    
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
    });
  </script>
</body>
</html>
