# AI Voice Assistant - Enterprise Conversational AI

A sophisticated, enterprise-grade AI voice assistant powered by Google's Gemini technology, built with modern web technologies and designed for professional environments.

## 🚀 Features

- **Advanced Voice Recognition**: Real-time speech-to-text processing with noise cancellation
- **Natural Conversations**: Powered by Google's Gemini 2.5 Flash model for intelligent responses
- **Enterprise Design**: Professional, polished UI/UX suitable for business environments
- **Real-time Audio**: Low-latency audio processing with WebRTC technology
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Progressive Web App**: Installable with offline capabilities
- **Accessibility**: WCAG 2.1 compliant with full keyboard navigation
- **Modern Architecture**: Built with TypeScript, Lit, and Vite

## 🛠 Technology Stack

- **Frontend Framework**: [Lit](https://lit.dev/) - Lightweight web components
- **Language**: TypeScript for type safety and developer experience
- **Build Tool**: [Vite](https://vitejs.dev/) for fast development and optimized builds
- **AI Integration**: [Google Generative AI](https://ai.google.dev/) with Gemini models
- **Audio Processing**: Web Audio API with real-time PCM streaming
- **Styling**: Modern CSS with design tokens and CSS custom properties
- **PWA**: Service Worker with Workbox for offline functionality

## 📋 Prerequisites

- Node.js 18.0.0 or higher
- npm 9.0.0 or higher
- Modern web browser with Web Audio API support
- Microphone access for voice input
- Google AI API key

## 🔧 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/enterprise-ai/voice-assistant.git
   cd voice-assistant
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and add your Google AI API key:
   ```
   API_KEY=your_google_ai_api_key_here
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:3000`

## 🚀 Development

### Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally
- `npm run lint` - Run ESLint for code quality
- `npm run lint:fix` - Fix ESLint issues automatically
- `npm run format` - Format code with Prettier
- `npm run type-check` - Run TypeScript type checking
- `npm run test` - Run unit tests
- `npm run test:coverage` - Run tests with coverage report

### Project Structure

```
src/
├── components/          # Web components
│   ├── gdm-live-audio.ts   # Main audio component
│   ├── app-layout.ts       # Application layout
│   └── utils.ts            # Utility functions
├── styles/              # Global styles and design system
│   └── design-system.css   # Design tokens and utilities
└── main.ts              # Application entry point
```

### Design System

The application uses a comprehensive design system with:

- **Design Tokens**: Consistent colors, typography, spacing, and shadows
- **Component Library**: Reusable UI components with consistent styling
- **Responsive Design**: Mobile-first approach with breakpoint utilities
- **Dark Theme**: Professional dark theme optimized for extended use
- **Accessibility**: ARIA labels, focus management, and keyboard navigation

## 🎨 Customization

### Theming

Modify design tokens in `src/styles/design-system.css`:

```css
:root {
  --color-primary-500: #your-brand-color;
  --color-background: #your-background-color;
  /* ... other tokens */
}
```

### Branding

Update branding in `src/components/app-layout.ts`:

```typescript
@property({type: String}) title = 'Your Company Name';
@property({type: String}) subtitle = 'Your tagline here';
```

## 🔒 Security

- **API Key Protection**: Environment variables for sensitive data
- **Content Security Policy**: Implemented for XSS protection
- **HTTPS Only**: Enforced in production environments
- **Input Validation**: All user inputs are validated and sanitized

## 🌐 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 📱 Progressive Web App

The application is a fully functional PWA with:

- **Offline Support**: Core functionality available without internet
- **Install Prompt**: Users can install the app on their devices
- **App-like Experience**: Full-screen mode with native app feel
- **Background Sync**: Queued actions when connectivity is restored

## 🧪 Testing

Run the test suite:

```bash
npm run test
```

For coverage reports:

```bash
npm run test:coverage
```

## 📦 Deployment

### Build for Production

```bash
npm run build
```

The `dist/` folder contains the production-ready files.

### Environment Variables

Set the following environment variables in your deployment:

- `API_KEY`: Your Google AI API key
- `NODE_ENV`: Set to `production`

### Recommended Hosting

- **Vercel**: Zero-config deployment with automatic HTTPS
- **Netlify**: Easy deployment with form handling
- **Firebase Hosting**: Google Cloud integration
- **AWS S3 + CloudFront**: Enterprise-grade hosting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs.ai-voice-assistant.com](https://docs.ai-voice-assistant.com)
- **Issues**: [GitHub Issues](https://github.com/enterprise-ai/voice-assistant/issues)
- **Discussions**: [GitHub Discussions](https://github.com/enterprise-ai/voice-assistant/discussions)
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- Google AI team for the Gemini API
- Lit team for the excellent web components framework
- The open-source community for the amazing tools and libraries

---

**Built with ❤️ for the enterprise**
